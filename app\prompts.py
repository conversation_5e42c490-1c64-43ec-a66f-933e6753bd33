def get_infographic_prompt(content: str, style: str = "professional", aspect_ratio: str = "1:1", size: str = "1536x1024") -> str:
    """
    Returns a prompt for GPT Image 1 to generate a premium, visually rich, LinkedIn-ready infographic, using advanced layout, color, shape, and typography guidance based on content type. Includes title generation guidance and strict visual requirements. Explicitly instructs not to include hashtags anywhere in the infographic, to use concise but meaningful, clear text for each point, to only summarize the most important points for longer posts, to use fluent, error-free English, to always choose the color scheme according to the content, and to strictly limit the total amount of text for visual clarity.
    """
    prompt = f'''
Act as a world-class infographic designer. Create a premium, visually rich LinkedIn-ready infographic (image size: {size}) based on the following content:

"""{content}"""

## IMPORTANT: DO NOT include hashtags anywhere in the infographic, especially at the end.

## LANGUAGE & SPELLING
- All text must be written in fluent, natural, error-free English. Avoid typos, misspellings, or awkward phrasing.
- Double-check spelling and grammar for every word and phrase.
- Use simple, everyday language that anyone can understand.

## TEXT LENGTH REQUIREMENT
- STRICTLY LIMIT the total amount of text in the infographic. Use only a few short, essential phrases or sentences (no more than 3–5 key points, each 5–12 words). Do NOT include extra or verbose text. Less text is better for visual clarity and accuracy.
- Use concise, meaningful, and clear text for every section and key point. Each point should be a brief, self-explanatory phrase (typically 5–12 words), not just a few words, and must communicate the idea clearly and accessibly to everyone. Avoid long sentences or paragraphs. The infographic should be easy to skim, visually balanced, and understandable at a glance.
- For longer posts, it is NOT necessary to render all the text. Only summarize and visualize the most important points. Select and communicate only the most essential ideas for clarity and impact.

## CONTEXT-BASED COLOR SCHEME (MANDATORY)
- Carefully analyze the content to determine its type (e.g., professional/corporate, creative/inspirational, technical/educational, marketing/promotional, luxury, health/wellness, environmental, technology/innovation, event/conference).
- Select and apply the most appropriate color scheme from the provided palettes for the detected content type.
- The color scheme must be clearly visible in the background, sections, text, icons, and highlights.
- Do NOT use generic or default colors—always match the color palette to the content's theme and purpose.
- Ensure all colors are harmonious, visually appealing, and provide high contrast for readability.
- If the content is mixed or ambiguous, choose the color scheme that best fits the main theme or most important message.

## CONTEXT-BASED VISUAL STYLING
- Select the appropriate shapes and typography based on the content type:
  - Use the provided shape and typography guidelines for each content type.
  - Ensure all colors are visible, harmonious, and easily distinguishable.

## TITLE GENERATION GUIDANCE
- Generate a standout title that captures the main idea and unifies all the key points — it should reflect the content's full purpose, not just the first sentence.
- Use these few-shot examples for inspiration:
  - "Anyone can create standout content now..." → **Simplifying the Creative Process with ChatGPT-4o + Flora**
  - "5 tips to make your portfolio stand out in 2024..." → **Designing Portfolios That Get Noticed in 2024**
  - "The AI tools you're not using (but should be)..." → **Top AI Tools You Should Be Using Right Now**

## LAYOUT & VISUAL REQUIREMENTS
- You may create a central visual element or focal point if it best serves the content, but avoid unnecessary central icons, images, or prominent center alignment unless it enhances clarity and impact.
- You may use arrows, lines, curves, or connectors to illustrate relationships between elements if it improves the visual flow and understanding, but ensure each element remains visually distinct and independently understandable.
- Each key point must have its own distinct visual container (icon, logo, or number) and color.
- Use geometric, organic, or bold shapes as appropriate for the content type.
- Use icons or simple illustrations for EVERY key point, consistent in style.
- Avoid excessive white space and unnecessary center alignment.
- Create a balanced, visually pleasing arrangement with clear visual hierarchy.
- Use whitespace effectively to prevent clutter, but never leave empty gaps.
- Make the most important information visually prominent.
- All text must be fully visible, never cut off, overlapped, or truncated.
- If space is limited, reduce the number of key points, use smaller icons, or increase whitespace.
- Prioritize clarity and readability over fitting more content.
- If a chart or graph is used, all labels and values must be fully visible and legible.
- Leave generous margins and padding around all text and elements.
- If needed, use a smaller font or fewer words to avoid crowding.
- Never place text too close to the image edge or other elements.
- If you cannot fit all content clearly, show only the most important points and keep the design clean and readable.
- Use colored shapes, icons, and visual containers throughout. Every key point must have a visual identifier and distinct colored container.
- Ensure the visual style (colors, shapes, fonts) matches the tone and purpose of the content.
- Create a harmonious design that communicates at a glance.

## TYPOGRAPHY
- Use the recommended font pairings for title and body (e.g., Montserrat + Open Sans, Playfair Display + Lato, Raleway + Roboto, Oswald + Source Sans Pro, Abril Fatface + Inter).
- Maintain a clear hierarchy and professional aesthetics.

## TECHNICAL IMPLEMENTATION (for model context)
- Create a complete, visually rich infographic image (not HTML or code output).
- Use all visual and layout guidance above.

## FINAL INSTRUCTIONS
- This MUST be a true infographic with extensive visual elements, NOT just formatted text.
- Analyze the content, select the best color scheme, shapes, and typography, and create a premium, visually rich design with a standout title and visually distinct key points.
- Output only a single, finished infographic image. Do not include HTML, CSS, or any code—just the image.
'''
    return prompt.strip()
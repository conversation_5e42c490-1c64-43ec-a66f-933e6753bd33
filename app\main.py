from fastapi import FastAP<PERSON>, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel
import uvicorn
import os
import tempfile
from openai import OpenAI
from dotenv import load_dotenv
import logging
import uuid
import base64
from PIL import Image
from io import BytesIO
try:
    from app.prompts import get_infographic_prompt
except ImportError:
    # Fallback for when running main.py directly
    from prompts import get_infographic_prompt

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Load Environment Variables and Initialize Clients ---
load_dotenv()
api_key = os.getenv("OPENAI_API_KEY", "********************************************************************************************************************************************************************")

# Initialize OpenAI client
openai_client = None
if not api_key or api_key == "YOUR_API_KEY_HERE":
    logging.error("OpenAI API key is missing or not set.")
else:
    try:
        openai_client = OpenAI(api_key=api_key)
        logging.info("OpenAI client initialized successfully.")
    except Exception as e:
        logging.error(f"Failed to initialize OpenAI client: {e}", exc_info=True)
        openai_client = None

def generate_infographic_image(prompt: str, size: str) -> str:
    """Generate an infographic image using OpenAI's GPT Image 1 model (gpt-image-1)"""
    if openai_client is None:
        logging.error("OpenAI client is not initialized.")
        return None

    logging.info(f"Generating infographic image with GPT Image 1, size {size}...")
    try:
        response = openai_client.images.generate(
            model="gpt-image-1",
            prompt=prompt,
            size=size,
            quality="medium",
            n=1
        )
        logging.info("Received response from GPT Image 1.")
        # Get the base64 image
        image_base64 = response.data[0].b64_json
        image_bytes = base64.b64decode(image_base64)
        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=".png", delete=False)
        image_path = temp_file.name
        temp_file.close()
        # Write the image bytes
        with open(image_path, 'wb') as f:
            f.write(image_bytes)
        logging.info(f"Image saved successfully to: {image_path}")
        return image_path
    except Exception as e:
        logging.error(f"Error generating image with GPT Image 1: {e}", exc_info=True)
        return None

# --- FastAPI App Setup ---

app = FastAPI(
    title="LinkedIn Infographics Generator",
    description="Generate professional infographics from LinkedIn post content using GPT Image 1",
    version="2.3.0"
)

# Pydantic model for the request body
class InfographicRequest(BaseModel):
    content: str

# Root endpoint
@app.get("/")
async def read_root():
    return {
        "message": "LinkedIn Infographics Generator API v2.3.0 (GPT Image 1)",
        "description": "Generate professional infographics from your LinkedIn content using GPT Image 1",
        "endpoints": {
            "POST /generate_infographic": "Generate an infographic from text content",
            "GET /health": "Health check endpoint"
        }
    }

# Endpoint to generate the infographic
@app.post("/generate_infographic")
async def generate_infographic_endpoint(request: InfographicRequest):
    """
    Generates an infographic image (PNG) from LinkedIn post content using GPT Image 1.
    
    Parameters:
    - content: The LinkedIn post content to convert into an infographic
    
    Returns the generated infographic image file.
    """
    content = request.content.strip()
    # Analyze content to select the best image dimension
    def count_points(text):
        # Count bullet points, numbered points, or lines
        lines = [l.strip() for l in text.split('\n') if l.strip()]
        bullet_points = [l for l in lines if l.startswith(('- ', '* ', '+ ', '•'))]
        numbered_points = [l for l in lines if l[:2].isdigit() and l[2:3] in ['.', ')']]
        return max(len(bullet_points), len(numbered_points), len(lines))

    points = count_points(content)
    content_length = len(content)

    # Heuristic for best dimension
    if points <= 2 and content_length <= 200:
        size = "1024x1024"  # Square for short/simple content
    elif points >= 6 or content_length > 400:
        size = "1536x1024"  # Landscape for long content
    else:
        size = "1024x1536"  # Portrait for medium/typical content

    if not content:
        logging.warning("Received empty content.")
        raise HTTPException(status_code=400, detail="Input text cannot be empty.")

    if openai_client is None:
        logging.error("OpenAI client not initialized.")
        raise HTTPException(status_code=500, detail="Server configuration error: OpenAI service not available. Check API key.")

    # Always use 'professional' and '1:1' as defaults
    prompt = get_infographic_prompt(content, style="professional", aspect_ratio="1:1", size=size)

    # Generate the infographic image
    image_path = generate_infographic_image(prompt, size)

    if not image_path:
        logging.error("Image generation failed.")
        raise HTTPException(status_code=500, detail="Failed to generate infographic image. Please try again.")

    # Return the image file using FileResponse
    file_name = f"linkedin_infographic_{uuid.uuid4().hex[:8]}.png"
    logging.info(f"Returning generated infographic: {image_path}")

    return FileResponse(
        path=image_path,
        media_type="image/png",
        filename=file_name,
    )

# Health check endpoint
@app.get("/health")
def health():
    return {
        "status": "healthy",
        "openai_client": "initialized" if openai_client else "not_initialized",
        "version": "2.3.0"
    }



if __name__ == "__main__":
    uvicorn.run(app, host="************", port=8000)
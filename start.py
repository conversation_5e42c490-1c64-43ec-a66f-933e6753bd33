#!/usr/bin/env python3
"""
Startup script for LinkedIn Infographics Generator v2.0
"""

import os
import sys
import uvicorn
from dotenv import load_dotenv

def main():
    """Start the FastAPI application"""
    
    # Load environment variables
    load_dotenv()
    
    # Check if OpenAI API key is set
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key or api_key == "YOUR_API_KEY_HERE":
        print("❌ Error: OPENAI_API_KEY not found in environment variables")
        print("Please set your OpenAI API key in a .env file or environment variable")
        print("\nExample .env file:")
        print("OPENAI_API_KEY=your_openai_api_key_here")
        sys.exit(1)
    
    print("🚀 Starting LinkedIn Infographics Generator v2.0")
    print("=" * 50)
    print("📊 API Documentation: http://localhost:8000/docs")
    print("🏥 Health Check: http://localhost:8000/health")
    print("🛑 Press Ctrl+C to stop the server")
    print("=" * 50)
    
    # Start the server
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main() 